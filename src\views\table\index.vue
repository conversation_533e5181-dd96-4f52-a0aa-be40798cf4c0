<script setup lang="ts">
import { initRouter } from "@/router/utils";
import { storageLocal } from "@pureadmin/utils";
import {
  type CSSProperties,
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  defineAsyncComponent,
  reactive,
  Ref,
  DefineComponent
} from "vue";
import { useRoute } from "vue-router";
import { customList } from "@/utils/custom";
import detail1 from "@/views/custom/details.vue";
const form = ref({});
const basicForm = ref({});
const count: Number = null;
const pageSelect: Array<any> = [10, 20, 50, 100];
const comeJson = reactive({
  json: {}
});
let formQuery = [
  {
    type: "input",
    key: "userId",
    label: "用户ID"
  },
  {
    type: "input",
    key: "username",
    label: "姓名"
  },
  {
    type: "input",
    key: "phone",
    label: "手机号"
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];
interface User {
  date: string;
  name: string;
  address: string;
}
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "name",
    label: "头像",
    width: ""
  },
  {
    property: "name",
    label: "手机号码",
    width: ""
  },
  {
    property: "name",
    label: "姓名",
    width: ""
  },
  {
    property: "name",
    label: "年龄",
    width: ""
  },
  {
    property: "name",
    label: "提交时间",
    width: ""
  },
  {
    property: "name",
    label: "审批时间",
    width: ""
  },
  {
    property: "审核状态",
    label: "审批时间",
    width: ""
  }
];
const tableData: User[] = [
  {
    date: "2016-05-04",
    name: "Aleyna Kutzner",
    address: "Lohrbergstr. 86c, Süd Lilli, Saarland"
  },
  {
    date: "2016-05-03",
    name: "Helen Jacobi",
    address: "760 A Street, South Frankfield, Illinois"
  },
  {
    date: "2016-05-02",
    name: "Brandon Deckert",
    address: "Arnold-Ohletz-Str. 41a, Alt Malinascheid, Thüringen"
  },
  {
    date: "2016-05-01",
    name: "Margie Smith",
    address: "23618 Windsor Drive, West Ricardoview, Idaho"
  }
];
import type { ComponentSize } from "element-plus";
const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};
const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}
onMounted(() => {
  customList.forEach(res => {
    console.log(res);
  });
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
function successEdit() {}
const childRef = ref(null);
const showComponent = computed(() => {
  return detail1; //按定义渲染组件
});
const showDialog = ref(false);
function getDetail(item) {
  comeJson.json = customList[0];
  comeJson.json.component = defineAsyncComponent(comeJson.json.component);
  nextTick(() => {
    setTimeout(() => {
      comeJson.json.data = item;
      showDialog.value = true;
    }, 500);
  });
}
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header">
        <div
          v-for="(item, index) of formQuery"
          :key="index"
          :style="{
            width:
              index % 2
                ? formQuery.length - 1 == index
                  ? '25%'
                  : '66%'
                : '25%'
          }"
        >
          <div class="billsplit-row">
            <div style="width: 75px; letter-spacing: 1px; font-size: 14px">
              <span>{{ item.label }}</span>
            </div>
            <div>
              <el-input
                v-show="item.type == 'input'"
                v-model="form[item.key]"
                size="large"
                class="w-[232px]!"
                :placeholder="'请输入' + item.label"
                clearable
              />
              <el-input
                v-show="item.type == 'datetime'"
                v-model="form[item.key]"
                size="large"
                class="w-[232px]!"
                :placeholder="'请输入' + item.label"
                clearable
              />
            </div>
          </div>
        </div>
        <div style="width: 50%; display: flex; justify-content: flex-end">
          <el-button size="large" type="primary">搜索</el-button>
          <el-button
            size="large"
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            >重置</el-button
          >
        </div>
      </div>
      <div></div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        :data="tableData"
        style="width: 100%"
        border
        ref="tableContainer"
        :height="tableHeight"
      >
        <el-table-column type="selection" width="60"></el-table-column>
        <el-table-column
          v-for="(config, index) of tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          :property="config.property"
        ></el-table-column>
        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click.prevent="getDetail(scope)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <component
      :is="comeJson.json.component"
      :showDialog="showDialog"
    ></component>
  </div>
</template>
<style scoped lang="scss">
.billsplit-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  line-height: 60px;
}
.table-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 5px 20px;
  font-size: 14px;
  button {
    width: 74px !important;
    height: 40px !important;
  }
}
</style>
