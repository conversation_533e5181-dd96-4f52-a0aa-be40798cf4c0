# 国密加密HTTP拦截器

本项目已集成国密SM4加密算法到HTTP请求拦截器和响应拦截器中，实现了自动的数据加密和解密功能。

## 功能特性

- **自动加密**: 根据URL配置自动对请求数据进行SM4加密
- **自动解密**: 根据URL配置自动对响应数据进行SM4解密
- **智能识别**: 基于`useBaseUrlList`配置判断哪些接口需要加密处理
- **错误处理**: 加密/解密失败时的优雅降级处理
- **类型安全**: 完整的TypeScript类型支持

## 核心文件

### 1. `crypto-interceptor.ts`
国密加密拦截器工具类，提供：
- `handleRequestEncrypt()`: 请求加密处理
- `handleResponseDecrypt()`: 响应解密处理
- `shouldEncrypt()`: 判断URL是否需要加密

### 2. `index.ts`
HTTP客户端主文件，已集成加密拦截器

### 3. `useSmCrypto.ts`
国密加密hooks，提供：
- `sm4Encrypt()`: 数据加密
- `sm4Decrypt()`: 数据解密
- `sm4DecryptToString()`: 解密为字符串

### 4. `useBaseUrlList.ts`
URL配置管理hooks，提供：
- `baseUrlUriIsEncrypt()`: 判断URL是否需要加密

## 使用方式

### 基本使用
```typescript
import { http } from "@/utils/http";

// 发送请求，如果URL在加密列表中，数据会自动加密
const response = await http.post("/api/user/login", {
  username: "admin",
  password: "123456"
});

// 响应数据会自动解密（如果需要）
console.log(response.data);
```

### 手动控制加密
```typescript
import { cryptoInterceptor } from "@/utils/http/crypto-interceptor";

// 检查URL是否需要加密
const needEncrypt = cryptoInterceptor.shouldEncrypt("/api/user/login");

// 手动处理请求配置
const config = cryptoInterceptor.handleRequestEncrypt({
  url: "/api/user/login",
  data: { username: "admin", password: "123456" }
});
```

## 配置说明

### 环境变量
在`.env`文件中配置SM4密钥：
```
VITE_APP_SM4_KEY=your_sm4_key_here
```

### 加密URL配置
通过`useBaseUrlList`管理需要加密的URL列表：
```typescript
// 在useBaseUrlList.ts中配置
const baseUrlEncryptUri = computed(() =>
  arrayRemoveRepeat(
    baseUrlEncryptList.value
      .map(index => index.uri)
      .concat(["/easyzhipin-api/msg/list"]) // 添加需要加密的URL
  )
);
```

## 工作流程

### 请求流程
1. 发送HTTP请求
2. 请求拦截器检查URL是否需要加密
3. 如果需要加密，使用SM4算法加密请求数据
4. 添加`X-Encrypt: 1`请求头标识
5. 发送加密后的请求

### 响应流程
1. 接收HTTP响应
2. 响应拦截器检查URL是否需要解密
3. 如果需要解密，使用SM4算法解密响应数据
4. 智能处理不同数据格式（字符串、对象、数组）
5. 返回解密后的数据

## 错误处理

- 加密失败：记录错误日志，发送原始数据
- 解密失败：记录警告日志，返回原始响应
- 配置错误：优雅降级，不影响正常请求

## 注意事项

1. **密钥安全**: 确保SM4密钥的安全性，不要在代码中硬编码
2. **性能考虑**: 加密/解密会增加一定的性能开销
3. **兼容性**: 确保后端接口支持相同的加密算法和格式
4. **调试**: 开发环境下会输出加密/解密的详细日志

## 扩展功能

### 自定义加密字段
可以在`crypto-interceptor.ts`中修改`encryptFields`数组来自定义需要解密的字段名：
```typescript
const encryptFields = ['data', 'content', 'result', 'body', 'customField'];
```

### 添加新的加密算法
可以扩展`CryptoInterceptor`类来支持其他加密算法：
```typescript
public handleRequestEncryptWithRSA(config: PureHttpRequestConfig): PureHttpRequestConfig {
  // RSA加密实现
}
```

## 故障排除

### 常见问题
1. **加密失败**: 检查SM4密钥配置是否正确
2. **解密失败**: 确认后端返回的数据格式是否正确
3. **URL不匹配**: 检查`useBaseUrlList`中的URL配置

### 调试方法
1. 查看浏览器控制台的加密/解密日志
2. 检查网络请求中的`X-Encrypt`请求头
3. 验证环境变量配置是否正确
