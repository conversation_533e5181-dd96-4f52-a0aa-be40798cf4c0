/**
 * 国密加密HTTP拦截器测试示例
 * 
 * 这个文件展示了如何使用集成了国密加密功能的HTTP客户端
 */

import { http } from "./index";
import { cryptoInterceptor } from "./crypto-interceptor";

/**
 * 测试加密请求
 */
export async function testEncryptedRequest() {
  try {
    console.log("=== 测试加密请求 ===");
    
    // 模拟登录请求（假设这个URL在加密列表中）
    const loginData = {
      username: "admin",
      password: "123456",
      timestamp: Date.now()
    };
    
    console.log("原始请求数据:", loginData);
    
    // 发送请求，数据会自动加密
    const response = await http.post("/easyzhipin-api/msg/list", loginData);
    
    console.log("响应数据:", response);
    
    return response;
  } catch (error) {
    console.error("加密请求测试失败:", error);
    throw error;
  }
}

/**
 * 测试普通请求（不加密）
 */
export async function testNormalRequest() {
  try {
    console.log("=== 测试普通请求 ===");
    
    const userData = {
      id: 1,
      name: "测试用户"
    };
    
    console.log("原始请求数据:", userData);
    
    // 发送请求到非加密URL
    const response = await http.post("/api/user/info", userData);
    
    console.log("响应数据:", response);
    
    return response;
  } catch (error) {
    console.error("普通请求测试失败:", error);
    throw error;
  }
}

/**
 * 测试手动加密功能
 */
export function testManualEncryption() {
  console.log("=== 测试手动加密功能 ===");
  
  const testData = {
    message: "这是一条测试消息",
    timestamp: Date.now(),
    user: {
      id: 1,
      name: "测试用户"
    }
  };
  
  console.log("原始数据:", testData);
  
  // 检查URL是否需要加密
  const shouldEncrypt1 = cryptoInterceptor.shouldEncrypt("/easyzhipin-api/msg/list");
  const shouldEncrypt2 = cryptoInterceptor.shouldEncrypt("/api/user/info");
  
  console.log("URL加密检查结果:");
  console.log("  /easyzhipin-api/msg/list:", shouldEncrypt1);
  console.log("  /api/user/info:", shouldEncrypt2);
  
  // 手动处理请求配置
  const config1 = cryptoInterceptor.handleRequestEncrypt({
    url: "/easyzhipin-api/msg/list",
    method: "POST",
    data: testData,
    headers: {}
  });
  
  const config2 = cryptoInterceptor.handleRequestEncrypt({
    url: "/api/user/info",
    method: "POST", 
    data: testData,
    headers: {}
  });
  
  console.log("处理后的配置:");
  console.log("  加密URL配置:", config1);
  console.log("  普通URL配置:", config2);
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log("开始运行国密加密HTTP拦截器测试...");
  
  try {
    // 测试手动加密功能
    testManualEncryption();
    
    // 测试普通请求
    await testNormalRequest();
    
    // 测试加密请求
    await testEncryptedRequest();
    
    console.log("所有测试完成！");
  } catch (error) {
    console.error("测试过程中出现错误:", error);
  }
}

// 在开发环境下可以调用这个函数进行测试
if (import.meta.env.DEV) {
  // runAllTests();
}

/**
 * 使用示例
 */
export const examples = {
  // 基本使用
  async basicUsage() {
    // 发送到加密接口
    const encryptedResponse = await http.post("/easyzhipin-api/msg/list", {
      query: "test",
      page: 1
    });
    
    // 发送到普通接口
    const normalResponse = await http.get("/api/user/profile");
    
    return { encryptedResponse, normalResponse };
  },
  
  // 带配置的请求
  async requestWithConfig() {
    const response = await http.post(
      "/easyzhipin-api/msg/list",
      { data: "test" },
      {
        timeout: 5000,
        beforeRequestCallback: (config) => {
          console.log("请求前回调:", config);
        },
        beforeResponseCallback: (response) => {
          console.log("响应前回调:", response);
        }
      }
    );
    
    return response;
  },
  
  // 错误处理
  async errorHandling() {
    try {
      const response = await http.post("/easyzhipin-api/msg/list", {
        invalidData: "这可能导致加密失败"
      });
      return response;
    } catch (error) {
      console.error("请求失败:", error);
      // 即使加密失败，也会尝试发送原始数据
      throw error;
    }
  }
};
