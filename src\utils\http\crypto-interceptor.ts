import type { PureHttpRequestConfig, PureHttpResponse } from "./types.d";
import { useSmCrypto } from "@/store/modules/useSmCrypto";
import { useBaseUrlList } from "@/store/modules/useBaseUrlList";

/**
 * 国密加密HTTP拦截器工具类
 */
export class CryptoInterceptor {
  private static instance: CryptoInterceptor;

  private constructor() {}

  public static getInstance(): CryptoInterceptor {
    if (!CryptoInterceptor.instance) {
      CryptoInterceptor.instance = new CryptoInterceptor();
    }
    return CryptoInterceptor.instance;
  }

  /**
   * 请求拦截器 - 加密处理
   * @param config 请求配置
   * @returns 处理后的请求配置
   */
  public handleRequestEncrypt(
    config: PureHttpRequestConfig
  ): PureHttpRequestConfig {
    try {
      const { sm4Encrypt } = useSmCrypto();
      const { baseUrlUriIsEncrypt } = useBaseUrlList();

      // 判断当前请求是否需要加密
      const needEncrypt = baseUrlUriIsEncrypt(config.url || "");

      if (needEncrypt && config.data) {
        // 对请求数据进行加密
        const encryptedData = sm4Encrypt(config.data);

        // 更新配置
        config.data = encryptedData;
        config.headers = {
          ...config.headers,
          "X-Encrypt": "1", // 添加加密标识
          "Content-Type": "application/json" // 确保内容类型正确
        };

        console.log("请求数据已加密:", {
          url: config.url,
          originalData: config.data,
          encryptedData
        });
      }

      return config;
    } catch (error) {
      console.error("请求加密处理失败:", error);
      return config;
    }
  }

  /**
   * 响应拦截器 - 解密处理
   * @param response 响应对象
   * @returns 处理后的响应对象
   */
  public handleResponseDecrypt(response: PureHttpResponse): PureHttpResponse {
    try {
      const { sm4Decrypt, sm4DecryptToString } = useSmCrypto();
      const { baseUrlUriIsEncrypt } = useBaseUrlList();

      const config = response.config;

      // 判断当前响应是否需要解密
      const needDecrypt = baseUrlUriIsEncrypt(config.url || "");

      if (needDecrypt && response.data) {
        const originalData = response.data;

        // 处理不同类型的响应数据
        if (typeof response.data === "string") {
          // 字符串类型 - 直接解密
          const decryptedString = sm4DecryptToString(response.data);

          // 尝试解析为JSON
          try {
            response.data = JSON.parse(decryptedString);
          } catch {
            // 解析失败，保持字符串格式
            response.data = decryptedString;
          }
        } else if (this.isObject(response.data)) {
          // 对象类型 - 处理可能的加密字段
          response.data = this.decryptObjectFields(
            response.data,
            sm4Decrypt,
            sm4DecryptToString
          );
        }

        console.log("响应数据已解密:", {
          url: config.url,
          originalData,
          decryptedData: response.data
        });
      }

      return response;
    } catch (error) {
      console.warn("响应解密处理失败:", error);
      // 解密失败时返回原始响应
      return response;
    }
  }

  /**
   * 递归解密对象中的加密字段
   * @param obj 需要解密的对象
   * @param sm4Decrypt 解密函数
   * @param sm4DecryptToString 解密为字符串函数
   * @returns 解密后的对象
   */
  private decryptObjectFields(
    obj: any,
    sm4Decrypt: (data: string) => any,
    sm4DecryptToString: (data: string) => string
  ): any {
    if (!this.isObject(obj)) {
      return obj;
    }

    const result = { ...obj };

    // 常见的可能需要解密的字段名
    const encryptFields = ["data", "content", "result", "body"];

    for (const key in result) {
      if (result.hasOwnProperty(key)) {
        const value = result[key];

        if (typeof value === "string" && encryptFields.includes(key)) {
          // 尝试解密字符串字段
          try {
            result[key] = sm4Decrypt(value);
          } catch {
            // 解密失败，尝试解密为字符串
            try {
              result[key] = sm4DecryptToString(value);
            } catch {
              // 都失败了，保持原值
              result[key] = value;
            }
          }
        } else if (this.isObject(value)) {
          // 递归处理嵌套对象
          result[key] = this.decryptObjectFields(
            value,
            sm4Decrypt,
            sm4DecryptToString
          );
        } else if (Array.isArray(value)) {
          // 处理数组
          result[key] = value.map(item =>
            this.isObject(item)
              ? this.decryptObjectFields(item, sm4Decrypt, sm4DecryptToString)
              : item
          );
        }
      }
    }

    return result;
  }

  /**
   * 判断是否为对象
   * @param obj 待判断的值
   * @returns 是否为对象
   */
  private isObject(obj: any): boolean {
    return obj !== null && typeof obj === "object" && !Array.isArray(obj);
  }

  /**
   * 检查URL是否需要加密处理
   * @param url 请求URL
   * @returns 是否需要加密
   */
  public shouldEncrypt(url: string): boolean {
    try {
      const { baseUrlUriIsEncrypt } = useBaseUrlList();
      return baseUrlUriIsEncrypt(url);
    } catch (error) {
      console.warn("检查URL加密状态失败:", error);
      return false;
    }
  }
}

// 导出单例实例
export const cryptoInterceptor = CryptoInterceptor.getInstance();
