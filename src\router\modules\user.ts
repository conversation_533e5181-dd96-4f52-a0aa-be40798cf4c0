export default {
  path: "/user",
  redirect: "/user/403",
  meta: {
    icon: "ri/information-line",
    // showLink: false,
    title: "用户审批",
    rank: 9
  },
  children: [
    {
      path: "/user/profile_picture",
      meta: {
        title: "头像"
      },
      children: [
        {
          path: "/user/403",
          name: "/user/403",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "待审核"
          }
        },
        {
          path: "/user/404",
          name: "/user/404",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "已通过"
          }
        },
        {
          path: "/user/405",
          name: "/user/405",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "已驳回"
          }
        }
      ]
    },
    {
      path: "/user/406",
      name: "/user/406",
      component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "证书"
      }
    },
    {
      path: "/user/407",
      name: "/user/407",
      component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "附件"
      }
    },
    {
      path: "/user/408",
      name: "/user/408",
      component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "作品集"
      }
    }
  ]
} satisfies RouteConfigsTable;
