import { arrayRemoveRepeat } from "@/utils";
import { msgList } from "@/service/msg";
import type { msgListInt } from "@/service/msg/types";

const baseUrlList = ref<msgListInt[]>([]);
/** 网关hooks */
export const useBaseUrlList = () => {
  const baseUrlMsgListIntApi = async () => {
    const { data } = await msgList();
    baseUrlList.value = data;
    uni.$isResolve();
  };
  const baseUrlEncryptList = computed(() =>
    baseUrlList.value.filter(index => index.encryptType === 1)
  );
  const baseUrlEncryptUri = computed(() =>
    arrayRemoveRepeat(
      baseUrlEncryptList.value
        .map(index => index.uri)
        .concat(["/easyzhipin-api/msg/list"])
    )
  );
  // const baseUrlUri = (url: string) => `/easyzhipin-api/${url}`
  const baseUrlUriIsEncrypt = (url: string) =>
    baseUrlEncryptUri.value.includes(url);
  return {
    baseUrlUriIsEncrypt,
    baseUrlMsgListIntApi
  };
};
