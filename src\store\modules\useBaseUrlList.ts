import { computed } from "vue";
import { arrayRemoveRepeat } from "@/utils";

// 定义需要加密的URL列表
const encryptUrls = [
  "/easyzhipin-api/msg/list"
  // 可以在这里添加更多需要加密的URL
];

/** 网关hooks */
export const useBaseUrlList = () => {
  // 获取需要加密的URL列表
  const baseUrlEncryptUri = computed(() => arrayRemoveRepeat(encryptUrls));

  // 判断URL是否需要加密
  const baseUrlUriIsEncrypt = (url: string) =>
    baseUrlEncryptUri.value.includes(url);

  return {
    baseUrlUriIsEncrypt
  };
};
