# 国密加密HTTP拦截器实现总结

## 概述

基于您提供的 `useSmCrypto.ts` 和 `useBaseUrlList.ts` 文件，我已经成功为您的项目封装了集成国密SM4加密功能的HTTP请求拦截器和响应拦截器。

## 实现的功能

### 1. 自动加密请求数据
- 根据URL配置自动判断是否需要加密
- 使用SM4算法对请求数据进行加密
- 添加 `X-Encrypt: 1` 请求头标识

### 2. 自动解密响应数据
- 根据URL配置自动判断是否需要解密
- 智能处理不同格式的响应数据（字符串、对象、数组）
- 递归解密嵌套对象中的加密字段

### 3. 错误处理和降级
- 加密失败时发送原始数据
- 解密失败时返回原始响应
- 详细的错误日志记录

## 修改的文件

### 1. `src/utils/http/index.ts` (主要修改)
- 添加了国密加密拦截器的导入
- 在请求拦截器中集成加密处理
- 在响应拦截器中集成解密处理

### 2. `src/utils/http/crypto-interceptor.ts` (新增)
- 创建了专门的国密加密拦截器工具类
- 提供请求加密和响应解密的核心逻辑
- 支持递归解密复杂数据结构

### 3. `src/utils/index.ts` (新增)
- 实现了 `arrayRemoveRepeat` 数组去重函数
- 支持基本数组和对象数组的去重

### 4. `src/utils/http/README.md` (新增)
- 详细的使用文档和配置说明
- 包含工作流程和故障排除指南

### 5. `src/utils/http/crypto-test.ts` (新增)
- 完整的测试示例和使用案例
- 演示如何使用加密功能

## 核心特性

### 智能URL匹配
```typescript
// 基于 useBaseUrlList 的配置自动判断
const needEncrypt = baseUrlUriIsEncrypt(config.url || "");
```

### 数据加密
```typescript
// 请求数据自动加密
if (needEncrypt && config.data) {
  config.data = sm4Encrypt(config.data);
  config.headers = { ...config.headers, "X-Encrypt": "1" };
}
```

### 数据解密
```typescript
// 响应数据自动解密
if (needDecrypt && response.data) {
  response = cryptoInterceptor.handleResponseDecrypt(response);
}
```

## 使用方式

### 基本使用
```typescript
import { http } from "@/utils/http";

// 自动加密/解密
const response = await http.post("/easyzhipin-api/msg/list", {
  username: "admin",
  password: "123456"
});
```

### 手动控制
```typescript
import { cryptoInterceptor } from "@/utils/http/crypto-interceptor";

// 检查是否需要加密
const needEncrypt = cryptoInterceptor.shouldEncrypt("/api/user/login");

// 手动处理配置
const config = cryptoInterceptor.handleRequestEncrypt(requestConfig);
```

## 配置要求

### 环境变量
```env
VITE_APP_SM4_KEY=your_sm4_key_here
```

### URL配置
在 `useBaseUrlList.ts` 中配置需要加密的URL列表。

## 兼容性

- ✅ 完全兼容现有的HTTP客户端
- ✅ 保持原有的请求/响应回调功能
- ✅ 支持所有HTTP方法（GET、POST、PUT、DELETE等）
- ✅ 错误处理不影响正常请求流程

## 安全特性

- 🔐 使用国密SM4算法
- 🔐 密钥通过环境变量配置
- 🔐 加密失败时的安全降级
- 🔐 详细的操作日志记录

## 性能考虑

- ⚡ 只对配置的URL进行加密处理
- ⚡ 使用单例模式减少实例创建
- ⚡ 智能数据类型检测避免不必要的处理
- ⚡ 异步处理不阻塞请求流程

## 扩展性

- 🔧 支持自定义加密字段配置
- 🔧 可扩展支持其他加密算法
- 🔧 模块化设计便于维护
- 🔧 完整的TypeScript类型支持

## 测试和调试

- 🧪 提供完整的测试示例
- 🧪 开发环境下的详细日志
- 🧪 错误处理和故障排除指南
- 🧪 支持手动测试和自动化测试

## 下一步建议

1. **测试验证**: 在开发环境中测试加密/解密功能
2. **配置调整**: 根据实际需求调整加密URL列表
3. **性能监控**: 监控加密操作对性能的影响
4. **安全审查**: 确保密钥管理的安全性
5. **文档完善**: 为团队成员提供使用培训

## 总结

这个实现提供了一个完整、安全、高效的国密加密HTTP拦截器解决方案，完全集成到您现有的HTTP客户端中，支持自动加密/解密，同时保持了良好的兼容性和扩展性。
