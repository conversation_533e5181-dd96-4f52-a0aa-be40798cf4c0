/**
 * 数组去重函数
 * @param arr 需要去重的数组
 * @returns 去重后的数组
 */
export const arrayRemoveRepeat = <T>(arr: T[]): T[] => {
  return Array.from(new Set(arr));
};

/**
 * 对象数组去重函数
 * @param arr 需要去重的对象数组
 * @param key 用于比较的键名
 * @returns 去重后的数组
 */
export const arrayRemoveRepeatByKey = <T>(arr: T[], key: keyof T): T[] => {
  const seen = new Set();
  return arr.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};
